export interface MSME {
  msme_id: string;
  name: string;
  business_type: 'retail' | 'b2b' | 'manufacturing' | 'services';
  location: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_trend?: 'improving' | 'declining' | 'stable';
  signals_count: number;
  recent_nudges: number;
  last_signal_date: string;
  created_at: string;
  tags: string[];
}

export interface Analytics {
  total_msmes: number;
  total_signals: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  business_type_distribution: {
    [key: string]: number;
  };
  average_signals_per_msme: number;
  last_updated: string;
}

export interface ScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_breakdown: {
    base_score: number;
    gst_penalty: number;
    reviews_penalty: number;
    upi_penalty: number;
    details: {
      [key: string]: string;
    };
  };
  signals_count: number;
  last_updated: string;
}

export type SignalSource = 'gst' | 'upi' | 'reviews' | 'justdial' | 'instagram' | 'maps';

export interface Signal {
  signal_id: string;
  msme_id: string;
  source: SignalSource;
  value: any; // Raw signal value (could be number, string, dict)
  normalized: number; // Normalized score 0-1
  timestamp: string;
  metadata: Record<string, any>;
}

export interface SignalInput {
  source: SignalSource;
  value: any;
  metadata?: Record<string, any>;
  timestamp?: string;
}

export interface Nudge {
  nudge_id: string;
  msme_id: string;
  trigger_type: 'score_drop' | 'manual' | 'risk_alert';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms';
  sent_at: string;
  status: 'sent' | 'delivered' | 'failed';
  metadata: Record<string, any>;
}

export interface NudgeRequest {
  trigger_type: 'score_drop' | 'manual' | 'risk_alert';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms';
  metadata?: Record<string, any>;
}

// Enhanced scoring system interfaces
export interface ScoreParameter {
  name: string;
  current_score: number;
  weight_percentage: number;
  penalty_impact: number;
  risk_level: 'low' | 'medium' | 'high';
  trend: 'improving' | 'declining' | 'stable';
  data_points: string[];
  methodology: string;
  last_updated: string;
}

export interface EnhancedScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  parameters: {
    gst_compliance: ScoreParameter;
    banking_health: ScoreParameter;
    digital_payment_adoption: ScoreParameter;
    business_legitimacy: ScoreParameter;
    market_reputation: ScoreParameter;
    financial_stability: ScoreParameter;
    industry_risk_factor: ScoreParameter;
    geographic_risk: ScoreParameter;
    operational_maturity: ScoreParameter;
    compliance_history: ScoreParameter;
  };
  total_penalty_impact: number;
  signals_count: number;
  last_updated: string;
}

// Top Actions and Opportunities interfaces
export interface TopAction {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  type: 'risk_reduction' | 'ltv_optimization' | 'portfolio_growth' | 'efficiency';
  msme_count: number;
  potential_value: string;
  action_items: string[];
  priority_score: number;
  estimated_completion_days: number;
}

export interface PortfolioInsights {
  total_msmes: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  top_actions: TopAction[];
  portfolio_health_score: number;
  diversification_index: number;
  data_completeness_score: number;
  automation_opportunities: number;
}
